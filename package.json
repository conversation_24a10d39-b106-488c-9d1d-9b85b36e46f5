{"name": "template-vite-react-ts-tailwind", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@nabla/vite-plugin-eslint": "2.0.4", "@types/eslint": "8.56.12", "@types/node": "20.12.13", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "@vitejs/plugin-react-swc": "3.7.0", "autoprefixer": "10.4.14", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "4.2.5", "eslint-plugin-import-x": "4.9.3", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.4", "postcss": "8.4.33", "prettier": "3.1.0", "prettier-plugin-tailwindcss": "0.4.1", "tailwindcss": "3.4.1", "typescript": "5.2.2", "vite": "5.4.15"}}